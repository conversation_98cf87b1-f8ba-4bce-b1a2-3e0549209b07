# =============================================================================
# Libra Project Environment Variables Configuration File (.env.example)
# =============================================================================
# This is a template file for environment variables required by the Libra project,
# containing all configuration items needed for application runtime.
# Please copy this file as .env and fill in the actual configuration values.
#
# 📋 Configuration Guide:
# • Required Configuration: Variables marked as [Required] are essential for normal application operation.
# • Optional Configuration: Variables marked as [Optional] can be left unset or use default values.
# • Security Reminder: Do not commit .env files containing real keys to the code repository.
#
# 🔗 Related Documentation: https://docs.libra.dev/environment-setup (coming soon)
# =============================================================================

# =============================================================================
# 1. Core Application Configuration (Required)
# =============================================================================
# Basic runtime configuration for the application, these are the minimum settings required to start the application.
#
# Required Configuration: NEXT_PUBLIC_APP_URL, NEXT_PUBLIC_CDN_URL
# Optional Configuration: NEXT_PUBLIC_SCAN
# Related Documentation: https://nextjs.org/docs/basic-features/environment-variables
# =============================================================================

# Application main domain URL | [Required] | Format: http://domain:port or https://domain
# Example: http://localhost:3000 (development) or https://your-domain.com (production)
# How to obtain: Set according to deployment environment
NEXT_PUBLIC_APP_URL=http://localhost:3000

# CDN resource URL | [Required] | Format: http://domain:port or https://domain
# Example: http://localhost:3004 (development) or https://cdn.your-domain.com (production)
# How to obtain: Set according to CDN configuration
NEXT_PUBLIC_CDN_URL=http://localhost:3004


# Deploy resource URL | [Required] | Format: http://domain:port or https://domain
# Example: http://localhost:3008 (development) or https://deploy.your-domain.com (production)
# How to obtain: Set according to Deploy configuration
NEXT_PUBLIC_DEPLOY_URL=http://localhost:3008

# DISPATCHER resource URL | [Required] | Format: http://domain:port or https://domain
# Example: http://localhost:3007 (development) or https://your-other-domain.com (production)
# How to obtain: Set according to Dispatcher configuration
# https://your-other-domain.com (production), meaningless in development environment
# Reference: https://developers.cloudflare.com/cloudflare-for-platforms/workers-for-platforms/
# Reference: https://developers.cloudflare.com/cloudflare-for-platforms/cloudflare-for-saas/
NEXT_PUBLIC_DISPATCHER_URL=https://your-other-domain.com

# react-scan security scan toggle | [Optional] | Format: 0 or 1
# Example: 1 (enable scanning) or 0 (disable scanning)
# How to obtain: Set according to security requirements, disable in production
NEXT_PUBLIC_SCAN=1

# =============================================================================
# 2. Authentication and Security
# =============================================================================
# Configuration related to user authentication, OAuth integration, and security.
# Magic Code and Github login
# Required Configuration: BETTER_AUTH_SECRET
# Optional Configuration: BETTER_GITHUB_CLIENT_ID, BETTER_GITHUB_CLIENT_SECRET, NEXT_PUBLIC_TURNSTILE_SITE_KEY
# Related Documentation: https://better-auth.com/docs, https://developers.cloudflare.com/turnstile/
# =============================================================================

# Better Auth secret key | [Required] | Format: Random string (at least 32 characters)
# Example: ****************************************
# How to obtain: Generate using openssl rand -hex 32
BETTER_AUTH_SECRET=your_auth_secret

# GitHub OAuth client ID | [Optional] | Format: GitHub application client ID
# Example: Iv1.**********abcdef
# How to obtain: GitHub Developer Settings > OAuth Apps
BETTER_GITHUB_CLIENT_ID=your_github_client_id

# GitHub OAuth client secret | [Optional] | Format: GitHub application client secret
# Example: ****************************************u1v2w3x4
# How to obtain: GitHub Developer Settings > OAuth Apps
BETTER_GITHUB_CLIENT_SECRET=your_github_client_secret

# GitHub OAuth App client ID for repository access | [Optional] | Format: GitHub OAuth App client ID
# Example: Iv1.**********abcdef
# How to obtain: GitHub Developer Settings > OAuth Apps (separate from better-auth)
GITHUB_OAUTH_CLIENT_ID=your_github_oauth_client_id

# GitHub OAuth App client secret for repository access | [Optional] | Format: GitHub OAuth App client secret
# Example: ****************************************u1v2w3x4
# How to obtain: GitHub Developer Settings > OAuth Apps (separate from better-auth)
GITHUB_OAUTH_CLIENT_SECRET=your_github_oauth_client_secret

# Cloudflare Turnstile site key | [Optional] | Format: Turnstile site key
# Documentation: https://developers.cloudflare.com/turnstile/
# Example: 0x**********ABCDEFabcdef**********
# How to obtain: Cloudflare Dashboard > Turnstile
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key

# Cloudflare Turnstile secret key | [Optional] | Format: Turnstile secret key
# Example: 0x4AAAAAABgQW-abcdef**********
TURNSTILE_SECRET_KEY=your_turnstile_secret_key

# =============================================================================
# 3. Data Storage Services
# =============================================================================
# Configuration related to database connections and data storage.
#
# Required Configuration: POSTGRES_URL
# Optional Configuration: DATABASE_ID
# Related Documentation: https://www.postgresql.org/docs/, https://neon.tech/docs
# =============================================================================

# PostgreSQL database connection URL | [Required] | Format: postgresql://user:password@host:port/dbname
# Example: postgresql://username:password@localhost:5432/libra_db
# How to obtain: Database provider or self-hosted PostgreSQL instance
POSTGRES_URL=your_postgres_url

# Database instance ID | [Optional] | Format: Database provider instance identifier
# Example: 12345678-1234-5678-1234-**********
# How to obtain: Cloudflare Dashboard > D1 > Database
DATABASE_ID=your_database_id

# =============================================================================
# 4. AI Services Integration
# =============================================================================
# API key configuration for various AI service providers, supporting multiple AI models.
#
# Required Configuration: At least one AI service
# Optional Configuration: Choose AI service providers according to needs
# Related Documentation: Official documentation for each AI service provider
# =============================================================================

# Anthropic Claude API key | [Optional] | Format: sk-ant-api03-...
# Example: sk-ant-api03-**********abcdef**********abcdef**********abcdef
# How to obtain: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key

# OpenAI API key | [Optional] | Format: sk-...
# Example: sk-proj-**********abcdef**********abcdef**********abcdef
# How to obtain: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key

# Google Gemini API key | [Optional] | Format: AIza...
# Example: AIzaSyA**********abcdef**********abcdef123
# How to obtain: https://ai.google.dev/
GEMINI_API_KEY=your_gemini_api_key

# xAI Grok API key | [Optional] | Format: xai-...
# Example: xai-**********abcdef**********abcdef**********
# How to obtain: https://console.x.ai/
XAI_API_KEY=your_xai_api_key

# DeepSeek API key | [Optional] | Format: sk-...
# Example: sk-**********abcdef**********abcdef**********
# How to obtain: https://platform.deepseek.com/
DEEPSEEK_API_KEY=your_deepseek_api_key

# OpenRouter API key | [Optional] | Format: sk-or-...
# Example: sk-or-v1-**********abcdef**********abcdef**********
# How to obtain: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key

# Custom API key | [Optional] | Format: According to custom API requirements
# Example: custom-api-key-**********abcdef
# How to obtain: According to custom API provider
CUSTOM_API_KEY=your_custom_api_key

# Azure OpenAI deployment name | [Optional] | Format: Azure deployment model name
# Example: gpt-4o-deployment or gpt-35-turbo-deployment
# How to obtain: Azure OpenAI Studio > Deployments
AZURE_DEPLOYMENT_NAME=your_azure_deployment_name

# Azure OpenAI resource name | [Optional] | Format: Azure OpenAI resource name
# Example: your-openai-resource
# How to obtain: Azure Portal > Azure OpenAI Service
AZURE_RESOURCE_NAME=your_azure_resource_name

# Azure OpenAI API key | [Optional] | Format: Azure key
# Example: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# How to obtain: Azure Portal > Azure OpenAI > Keys and Endpoint
AZURE_API_KEY=your_azure_api_key

# Azure OpenAI base URL | [Optional] | Format: https://resource_name.openai.azure.com
# Example: https://your-resource.openai.azure.com
# How to obtain: Azure Portal > Azure OpenAI > Keys and Endpoint
AZURE_BASE_URL=your_azure_base_url

# =============================================================================
# 5. Third-party Services Integration
# =============================================================================
# Configuration for payment, email, code execution, analytics, and other third-party services.
#
# Required Configuration: Choose according to business needs
# Optional Configuration: All configurations are optional, enable according to feature requirements
# Related Documentation: Integration documentation for each service provider
# =============================================================================

# Stripe payment webhook secret | [Required] | Format: whsec_...
# Example: whsec_**********abcdef**********abcdef**********
# How to obtain: Stripe Dashboard > Developers > Webhooks > Signing secret
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Stripe payment secret key | [Optional] | Format: sk_test_... or sk_live_...
# Example: sk_test_**********abcdef**********abcdef**********
# How to obtain: Stripe Dashboard > API keys
STRIPE_SECRET_KEY=your_stripe_secret_key

# Resend email service API key | [Optional] | Format: re_...
# Example: re_**********abcdef**********abcdef**********
# How to obtain: https://resend.com/api-keys
RESEND_API_KEY=your_resend_api_key

# Email sender address | [Optional] | Format: <EMAIL>
# Example: noreply@your-domain.<NAME_EMAIL>
# How to obtain: Configure your domain email
RESEND_FROM=<EMAIL>

# E2B code execution environment API key | [Optional] | Format: e2b_...
# Example: e2b_**********abcdef**********abcdef**********
# How to obtain: https://e2b.dev/dashboard
E2B_API_KEY=your_e2b_api_key

# Daytona code execution environment API key | [Optional] | Format: dtn_...
# Example: dtn_**********abcdef**********abcdef**********
# How to obtain: https://app.daytona.io/dashboard/settings
DAYTONA_API_KEY=your_daytona_api_key

# Default sandbox provider | [Required] | Format: e2b or daytona
# Example: e2b (use E2B as default sandbox) or daytona (use Daytona as default sandbox)
# How to obtain: Choose according to your sandbox provider
NEXT_PUBLIC_SANDBOX_DEFAULT_PROVIDER=daytona

# Default sandbox deployment builder provider | [Required] | Format: daytona or e2b
# Example: daytona (use Daytona as default sandbox) or e2b (use E2B as default sandbox)
# How to obtain: Choose according to your sandbox provider
NEXT_PUBLIC_SANDBOX_BUILDER_DEFAULT_PROVIDER=e2b


# PostHog analytics project API key | [Optional] | Format: phc_...
# Example: phc_**********abcdef**********abcdef**********
# How to obtain: PostHog Project Settings > API Keys
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_project_api_key

# PostHog analytics service host | [Optional] | Format: https://region.i.posthog.com
# Example: https://us.i.posthog.com or https://eu.i.posthog.com
# How to obtain: PostHog Project Settings > Project API Key
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# =============================================================================
# 6. Cloudflare Services Configuration
# =============================================================================
# Configuration for Cloudflare-related services, including AI Gateway, zone management, etc.
#
# Required Configuration: CLOUDFLARE_ACCOUNT_ID, CLOUDFLARE_API_TOKEN (if using Cloudflare services)
# Optional Configuration: CLOUDFLARE_AIGATEWAY_NAME, CLOUDFLARE_ZONE_ID
# Related Documentation: https://developers.cloudflare.com/
# =============================================================================

# Cloudflare account ID | [Required] | Format: 32-character hexadecimal string
# Example: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# How to obtain: Cloudflare Dashboard > Right sidebar Account ID
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id

# Cloudflare API token | [Required] | Format: Cloudflare API token
# Example: ****************************************
# How to obtain: Cloudflare Dashboard > My Profile > API Tokens > Create Token
# Libra API Token Summary
# This API token will affect the following accounts and zones, along with their respective permissions
# Libra - Browser Rendering:Edit, D1:Edit, Workers R2 Storage:Edit, Workers KV Storage:Edit, Workers Scripts:Edit
# All zones - Workers Routes:Edit, SSL and Certificates:Edit, Cache Purge:Purge
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# Cloudflare AI Gateway name | [Optional] | Format: Gateway name
# Example: libra-ai-gateway or production-gateway
# How to obtain: Cloudflare Dashboard > AI > AI Gateway
CLOUDFLARE_AIGATEWAY_NAME=your_cloudflare_aigateway_name

# Cloudflare zone ID | [Optional] | Format: 32-character hexadecimal string
# Main domain: libra.dev
# Example: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# How to obtain: Cloudflare Dashboard > Domain > Right sidebar Zone ID
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id

# =============================================================================
# 7. GitHub Integration Configuration
# =============================================================================
# Configuration related to GitHub repository access and project management.
#
# Required Configuration: If using GitHub integration features
# Optional Configuration: All configurations are optional
# Related Documentation: https://docs.github.com/en/rest
# =============================================================================

# GitHub access token | [Optional] | Format: ghp_... or github_pat_...
# Example: ghp_**********abcdef**********abcdef**********
# How to obtain: GitHub Settings > Developer settings > Personal access tokens
LIBRA_GITHUB_TOKEN=your_github_token

# GitHub organization or username | [Optional] | Format: GitHub username or organization name
# Example: your-username or your-organization
# How to obtain: Owner name in GitHub repository URL
LIBRA_GITHUB_OWNER=your_github_owner

# GitHub repository name | [Optional] | Format: Repository name
# Example: libra or your-project-name
# How to obtain: Repository name in GitHub repository URL
LIBRA_GITHUB_REPO=your_github_repo

# =============================================================================
# 8. Development and Debugging Configuration
# =============================================================================
# Configuration options for development environment, debugging, and experimental features.
#
# Required Configuration: None
# Optional Configuration: All configurations are optional, for development and debugging
# Related Documentation: Internal project documentation
# =============================================================================

# Log level | [Optional] | Format: DEBUG, INFO, WARN, ERROR
# Example: ERROR (production) or DEBUG (development)
# How to obtain: Set according to environment and debugging needs
LOG_LEVEL=ERROR

# Enhanced prompt mode | [Optional] | Format: TRUE or FALSE
# Example: TRUE (enable enhanced prompts) or FALSE (disable)
# How to obtain: Set according to feature requirements
ENHANCED_PROMPT=FALSE

# AI reasoning feature toggle | [Optional] | Format: TRUE or FALSE
# Example: TRUE (enable reasoning) or FALSE (disable)
# How to obtain: Set according to AI feature requirements
REASONING_ENABLED=FALSE

# Payload CMS secret | [Optional, disabled] | Format: Payload secret
# Example: payload-secret-key-**********abcdef
# How to obtain: Payload CMS configuration (currently unused)
# PAYLOAD_SECRET=your_payload_secret

# =============================================================================
# Configuration Completion Reminder
# =============================================================================
# 🎉 After completing the configuration, please rename this file to .env and restart the application.
# ⚠️ Make sure the .env file is added to .gitignore to avoid sensitive information leakage.
# 📖 For more configuration instructions, please refer to project documentation and official documentation of each service provider.
# =============================================================================

# =============================================================================
# 9. GitHub Application Integration Configuration
# =============================================================================
# GitHub application integration configuration for deep repository access and webhooks.
#
# Required Configuration: If using GitHub application features for repository management and automation
# Optional Configuration: All configurations are optional unless GitHub application features are needed
# Related Documentation: https://docs.github.com/en/developers/apps
# =============================================================================

# GitHub application slug name | [Optional] | Format: Application name
# Example: nextify-limited or your-app-name
# How to obtain: GitHub Developer Settings > GitHub Apps > App slug
GITHUB_APP_SLUG=nextify-limited

# GitHub application ID | [Optional] | Format: Numeric application ID
# Example: 1234567
# How to obtain: GitHub Developer Settings > GitHub Apps > App ID
GITHUB_APP_ID=your_github_app_id

# GitHub application private key | [Optional] | Format: RSA private key with newlines
# Example: "-----BEGIN RSA PRIVATE KEY-----
...
-----END RSA PRIVATE KEY-----"
# How to obtain: GitHub Developer Settings > GitHub Apps > Generate private key
GITHUB_APP_PRIVATE_KEY=your_github_app_private_key

# GitHub application OAuth client ID | [Optional] | Format: OAuth client identifier
# Example: **********1123
# How to obtain: GitHub Developer Settings > GitHub Apps > Client ID
GITHUB_APP_CLIENT_ID=your_github_app_client_id

# GitHub application OAuth client secret | [Optional] | Format: OAuth client secret
# Example: abcdef**********abcdef**********
# How to obtain: GitHub Developer Settings > GitHub Apps > Generate client secret
GITHUB_APP_CLIENT_SECRET=your_github_app_client_secret

# GitHub application public URL | [Optional] | Format: https://github.com/apps/app-name
# Example: https://github.com/apps/nextify-limited
# How to obtain: GitHub Developer Settings > GitHub Apps > Public link
NEXT_PUBLIC_GITHUB_APP_URL=https://github.com/apps/your_github_app_name

# GitHub webhook secret | [Optional] | Format: Webhook secret string
# Example: abcdef**********abcdef**********
# How to obtain: GitHub Developer Settings > GitHub Apps > Webhook secret
GITHUB_WEBHOOK_SECRET=your_github_webhook_secret

# =============================================================================
# 10. DNS and Domain Configuration
# =============================================================================
# Configuration for domain verification and DNS management, mainly for Cloudflare SaaS.
#
# Required Configuration: If using custom domains or Cloudflare SaaS features
# Optional Configuration: All configurations are optional unless domain features are needed
# Related Documentation: https://developers.cloudflare.com/cloudflare-for-platforms/cloudflare-for-saas/
# How to obtain: Cloudflare Dashboard > SaaS zone configuration

# Cloudflare DCV verification ID | [Required] | Format: 32-character hexadecimal string
# Example: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# How to obtain: Cloudflare Dashboard > SSL/TLS > Custom Hostnames > DCV Verification ID
# Purpose: Used for domain control verification to ensure SSL certificate issuance for custom domains
NEXT_PUBLIC_CLOUDFLARE_DCV_VERIFICATION_ID=your_dcv_verification_id

# Cloudflare SaaS zone ID | [Required] | Format: 32-character hexadecimal string
# Example: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# How to obtain: Cloudflare Dashboard > Cloudflare for SaaS > Zone Configuration
# Purpose: Manage custom domains and SSL certificates for SaaS customers
CLOUDFLARE_SAAS_ZONE_ID=your_cloudflare_saas_zone_id

# =============================================================================
# 11. Cloudflare Database and Storage Service IDs
# =============================================================================
# Resource identifiers for Cloudflare-specific services, used for database connections and key-value storage.

# Cloudflare Hyperdrive database ID | [Optional] | Format: 32-character hexadecimal string
# Example: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# How to obtain: Cloudflare Dashboard > Hyperdrive > Database Configuration
# Purpose: Accelerate database connections, providing globally distributed database access
HYPERDRIVE_ID=

# Cloudflare KV namespace ID | [Optional] | Format: 32-character hexadecimal string
# Example: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# How to obtain: Cloudflare Dashboard > Workers & Pages > KV > Namespace
# Purpose: Globally distributed key-value storage for caching and session management
KV_NAMESPACE_ID=

# =============================================================================
# 12. Network Routing Configuration
# =============================================================================
# IP address configuration for client routing and network access.

# Client APEX routing IP address | [Optional] | Format: IPv4 address
# Example: ************* or ***********
# How to obtain: Provided by network administrator or server configuration
# Purpose: Specify the primary routing IP address for client access, used to solve cname flattening issues
NEXT_PUBLIC_CUSTOMERS_IP_ADDRESS=

# =============================================================================
# 13. Screenshot Service Configuration
# =============================================================================
# Configuration for the screenshot service that captures project previews.

# Screenshot service URL | [Optional] | Format: http://domain:port or https://domain
# Example: http://localhost:3009 (development) or https://screenshot.libra.dev (production)
# How to obtain: Set according to your screenshot service deployment
# Purpose: URL of the screenshot service
# Default: http://localhost:3009
NEXT_PUBLIC_SCREENSHOT_SERVICE_URL=
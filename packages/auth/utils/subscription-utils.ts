/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * subscription-utils.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import { eq } from 'drizzle-orm'
import { getAuthDb, subscription } from '../db'

// Get user subscription from database
export async function getSubscription(userId: string) {
  const db = await getAuthDb() // Get database instance
  // Query subscription by user reference ID
  const subs = await db.query.subscription.findFirst({
    where: eq(subscription.referenceId, userId),
  })

  // Return default FREE plan if no subscription found
  return subs || { plan: 'FREE' }
}

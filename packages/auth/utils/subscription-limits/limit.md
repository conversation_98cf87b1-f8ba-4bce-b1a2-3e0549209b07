#### 1. `checkAndUpdateAIMessageUsage` - AI Message Quota

- **Location**: `packages/auth/utils/subscription-limits/core.ts`

- **Status**: ✅ Corrected

- **Logic**: FREE priority → Paid backup

- **Implementation**: Complete fast path and slow path

#### 2. `checkAndUpdateEnhanceUsage` - Enhanced Feature Quota

- **Location**: `packages/auth/utils/subscription-limits/core.ts`

- **Status**: ✅ Corrected

- **Logic**: FREE priority → Paid backup

- **Implementation**: Complete fast path and slow path

#### 3. `checkAndUpdateProjectUsage` - Project Creation Quota

- **Location**: `packages/auth/utils/subscription-limits/core.ts`

- **Status**: ✅ Corrected

- **Logic**: FREE priority → Paid backup

- **Implementation**: Complete fast path and slow path

#### 4. `checkAndUpdateDeployUsage` - Deployment Quota

- **Location**: `packages/auth/utils/subscription-limits/core.ts`

- **Status**: ✅ Corrected

- **Logic**: FREE priority → Paid backup

- **Implementation**: Complete fast path and slow path

### ✅ Corrected: uploadLimit Quota Logic

#### 5. `checkAndUpdateUploadUsage` - Upload Quota

- **Location**: `apps/cdn/src/utils/quota-management.ts`

- **Status**: ✅ Corrected

- **Logic**: FREE priority → Paid backup

- **Implementation**: Complete fast path and slow path
{"version": "6", "dialect": "sqlite", "id": "801161a0-4c49-4b88-b5e6-c8406761b58e", "prevId": "********-0000-0000-0000-************", "tables": {"account": {"name": "account", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "invitation": {"name": "invitation", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "inviter_id": {"name": "inviter_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"invitation_organization_id_organization_id_fk": {"name": "invitation_organization_id_organization_id_fk", "tableFrom": "invitation", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitation_inviter_id_user_id_fk": {"name": "invitation_inviter_id_user_id_fk", "tableFrom": "invitation", "tableTo": "user", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "member": {"name": "member", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'member'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"member_organization_id_organization_id_fk": {"name": "member_organization_id_organization_id_fk", "tableFrom": "member", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "member_user_id_user_id_fk": {"name": "member_user_id_user_id_fk", "tableFrom": "member", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "organization": {"name": "organization", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"organization_slug_unique": {"name": "organization_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "session": {"name": "session", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "region": {"name": "region", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "impersonated_by": {"name": "impersonated_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active_organization_id": {"name": "active_organization_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"session_token_unique": {"name": "session_token_unique", "columns": ["token"], "isUnique": true}}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscription": {"name": "subscription", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "reference_id": {"name": "reference_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'incomplete'"}, "period_start": {"name": "period_start", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "period_end": {"name": "period_end", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "banned": {"name": "banned", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "ban_reason": {"name": "ban_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ban_expires": {"name": "ban_expires", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "normalized_email": {"name": "normalized_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}, "user_normalized_email_unique": {"name": "user_normalized_email_unique", "columns": ["normalized_email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification": {"name": "verification", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "github_installation": {"name": "github_installation", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "installation_id": {"name": "installation_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_account_id": {"name": "github_account_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_account_login": {"name": "github_account_login", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_account_type": {"name": "github_account_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'{}'"}, "repository_selection": {"name": "repository_selection", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "installed_at": {"name": "installed_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"github_installation_installation_id_unique": {"name": "github_installation_installation_id_unique", "columns": ["installation_id"], "isUnique": true}, "github_installation_organization_id_idx": {"name": "github_installation_organization_id_idx", "columns": ["organization_id"], "isUnique": false}, "github_installation_installation_id_idx": {"name": "github_installation_installation_id_idx", "columns": ["installation_id"], "isUnique": false}, "github_installation_github_account_idx": {"name": "github_installation_github_account_idx", "columns": ["github_account_id"], "isUnique": false}, "github_installation_org_active_idx": {"name": "github_installation_org_active_idx", "columns": ["organization_id", "is_active"], "isUnique": false}}, "foreignKeys": {"github_installation_organization_id_organization_id_fk": {"name": "github_installation_organization_id_organization_id_fk", "tableFrom": "github_installation", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "github_user_token": {"name": "github_user_token", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_user_id": {"name": "github_user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_username": {"name": "github_username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_email": {"name": "github_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'bearer'"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"github_user_token_organization_idx": {"name": "github_user_token_organization_idx", "columns": ["organization_id"], "isUnique": false}, "github_user_token_github_user_idx": {"name": "github_user_token_github_user_idx", "columns": ["github_user_id"], "isUnique": false}, "github_user_token_github_username_idx": {"name": "github_user_token_github_username_idx", "columns": ["github_username"], "isUnique": false}}, "foreignKeys": {"github_user_token_organization_id_organization_id_fk": {"name": "github_user_token_organization_id_organization_id_fk", "tableFrom": "github_user_token", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "plan": {"name": "plan", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "price_id": {"name": "price_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "lookup_key": {"name": "lookup_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "annual_discount_price_id": {"name": "annual_discount_price_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "annual_discount_lookup_key": {"name": "annual_discount_lookup_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "limits": {"name": "limits", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'{}'"}, "marketing_features": {"name": "marketing_features", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "group": {"name": "group", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "price": {"name": "price", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "price_id": {"name": "price_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "unit_amount": {"name": "unit_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "price_type": {"name": "price_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pricing_plan_interval": {"name": "pricing_plan_interval", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "interval_count": {"name": "interval_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'{}'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"price_price_id_unique": {"name": "price_price_id_unique", "columns": ["price_id"], "isUnique": true}}, "foreignKeys": {"price_plan_id_plan_id_fk": {"name": "price_plan_id_plan_id_fk", "tableFrom": "price", "tableTo": "plan", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}
# =============================================================================

# Libra 项目环境变量配置文件 (.env.example)

# =============================================================================

# 这是一个 Libra 项目所需的环境变量模板文件，

# 包含了应用程序运行所需的所有配置项。

# 请根据实际需求复制此文件为 .env，并填写真实的配置值。

#

# 📋 配置指南：

# • 必需配置：标记为 [Required] 的变量是应用程序正常运行所必需的。

# • 可选配置：标记为 [Optional] 的变量可以不设置或使用默认值。

# • 安全提醒：请勿将包含真实密钥的 .env 文件提交到代码仓库。

#

# 🔗 相关文档：https://docs.libra.dev/environment-setup(coming soon)

# =============================================================================

# =============================================================================

# 1. 核心应用程序配置（必需）

# =============================================================================

# 应用程序的基本运行时配置，这些是启动应用程序所需的最低设置。

#

# 必需配置：NEXT_PUBLIC_APP_URL, NEXT_PUBLIC_CDN_URL

# 可选配置：NEXT_PUBLIC_SCAN

# 相关文档：https://nextjs.org/docs/basic-features/environment-variables

# =============================================================================

# 应用程序的主域名 URL | [必需] | 格式：http://domain:port 或 https://domain

# 示例：http://localhost:3000（开发环境）或 https://your-domain.com（生产环境）

# 获取方式：根据部署环境设置

NEXT_PUBLIC_APP_URL=http://localhost:3000

# CDN 资源 URL | [必需] | 格式：http://domain:port 或 https://domain

# 示例：http://localhost:3004（开发环境）或 https://cdn.your-domain.com（生产环境）

# 获取方式：根据 CDN 配置设置

NEXT_PUBLIC_CDN_URL=http://localhost:3004


# CDN 资源 URL | [必需] | 格式：http://domain:port 或 https://domain

# 示例：http://localhost:3008（开发环境）或 https://cdn.your-domain.com（生产环境）

# 获取方式：根据 Deploy 配置设置

NEXT_PUBLIC_DEPLOY_URL=http://localhost:3008

# DISPATCHER 资源 URL | [必需] | 格式：http://domain:port 或 https://domain

# 示例：http://localhost:3007（开发环境）或 https://your-other-domain.com（生产环境）

# 获取方式：根据 Dispatcher 配置设置

# https://your-other-domain.com（生产环境), 开发环境无意义
# 参考 https://developers.cloudflare.com/cloudflare-for-platforms/workers-for-platforms/
# 参考 https://developers.cloudflare.com/cloudflare-for-platforms/cloudflare-for-saas/

NEXT_PUBLIC_DISPATCHER_URL=https://your-other-domain.com

# react-scan 安全扫描开关 | [可选] | 格式：0 或 1

# 示例：1（启用扫描）或 0（禁用扫描）

# 获取方式：根据安全需求设置，生产环境禁用

NEXT_PUBLIC_SCAN=1

# =============================================================================

# 2. 身份验证和安全

# =============================================================================

# 与用户身份验证、OAuth 集成和安全相关的配置。

# Magic Code 以及 Github 登录

# 必需配置：BETTER_AUTH_SECRET

# 可选配置：BETTER_GITHUB_CLIENT_ID, BETTER_GITHUB_CLIENT_SECRET, NEXT_PUBLIC_TURNSTILE_SITE_KEY

# 相关文档：https://better-auth.com/docs, https://developers.cloudflare.com/turnstile/

# =============================================================================

# Better Auth 密钥 | [必需] | 格式：随机字符串（至少 32 个字符）

# 示例：****************************************

# 获取方式：使用 openssl rand -hex 32 生成

BETTER_AUTH_SECRET=your_auth_secret

# GitHub OAuth 客户端 ID | [可选] | 格式：GitHub 应用程序的客户端 ID

# 示例：Iv1.1234567890abcdef

# 获取方式：GitHub 开发者设置 > OAuth 应用程序

BETTER_GITHUB_CLIENT_ID=your_github_client_id

# GitHub OAuth 客户端密钥 | [可选] | 格式：GitHub 应用程序的客户端密钥

# 示例：****************************************u1v2w3x4

# 获取方式：GitHub 开发者设置 > OAuth 应用程序

BETTER_GITHUB_CLIENT_SECRET=your_github_client_secret

# Cloudflare Turnstile 站点密钥 | [可选] | 格式：Turnstile 站点密钥

# 文档：https://developers.cloudflare.com/turnstile/

# 示例：0x1234567890ABCDEFabcdef1234567890

# 获取方式：Cloudflare 仪表盘 > Turnstile

NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key

# Cloudflare Turnstile 密钥 | [可选] | 格式：Turnstile 密钥

# 示例：0x4AAAAAABgQW-abcdef1234567890

TURNSTILE_SECRET_KEY=your_turnstile_secret_key

# =============================================================================

# 3. 数据存储服务

# =============================================================================

# 与数据库连接和数据存储相关的配置。

#

# 必需配置：POSTGRES_URL

# 可选配置：DATABASE_ID

# 相关文档：https://www.postgresql.org/docs/, https://neon.tech/docs

# =============================================================================

# PostgreSQL 数据库连接 URL | [必需] | 格式：postgresql://user:password@host:port/dbname

# 示例：postgresql://username:password@localhost:5432/libra_db

# 获取方式：数据库提供商或自托管 PostgreSQL 实例

POSTGRES_URL=your_postgres_url

# 数据库实例 ID | [可选] | 格式：数据库提供商的实例标识符

# 示例：12345678-1234-5678-1234-1234567890

# 获取方式：Cloudflare 仪表盘 > D1 > 数据库

DATABASE_ID=your_database_id

# =============================================================================

# 4. AI 服务集成

# =============================================================================

# 各种 AI 服务提供商的 API 密钥配置，支持多个 AI 模型。

#

# 必需配置：至少配置一个 AI 服务

# 可选配置：根据需求选择 AI 服务提供商

# 相关文档：每个 AI 服务提供商的官方文档

# =============================================================================

# Anthropic Claude API 密钥 | [可选] | 格式：sk-ant-api03-...

# 示例：sk-ant-api03-1234567890abcdef1234567890abcdef1234567890abcdef

# 获取方式：https://console.anthropic.com/

ANTHROPIC_API_KEY=your_anthropic_api_key

# OpenAI API 密钥 | [可选] | 格式：sk-...

# 示例：sk-proj-1234567890abcdef1234567890abcdef1234567890abcdef

# 获取方式：https://platform.openai.com/api-keys

OPENAI_API_KEY=your_openai_api_key

# Google Gemini API 密钥 | [可选] | 格式：AIza...

# 示例：AIzaSyA1234567890abcdef1234567890abcdef123

# 获取方式：https://ai.google.dev/

GEMINI_API_KEY=your_gemini_api_key

# xAI Grok API 密钥 | [可选] | 格式：xai-...

# 示例：xai-1234567890abcdef1234567890abcdef1234567890

# 获取方式：https://console.x.ai/

XAI_API_KEY=your_xai_api_key

# DeepSeek API 密钥 | [可选] | 格式：sk-...

# 示例：sk-1234567890abcdef1234567890abcdef1234567890

# 获取方式：https://platform.deepseek.com/

DEEPSEEK_API_KEY=your_deepseek_api_key

# OpenRouter API 密钥 | [可选] | 格式：sk-or-...

# 示例：sk-or-v1-1234567890abcdef1234567890abcdef1234567890

# 获取方式：https://openrouter.ai/keys

OPENROUTER_API_KEY=your_openrouter_api_key

# 自定义 API 密钥 | [可选] | 格式：根据自定义 API 要求

# 示例：custom-api-key-1234567890abcdef

# 获取方式：根据自定义 API 提供商

CUSTOM_API_KEY=your_custom_api_key

# Azure OpenAI 部署名称 | [可选] | 格式：Azure 部署的模型名称

# 示例：gpt-4o-deployment 或 gpt-35-turbo-deployment

# 获取方式：Azure OpenAI 工作室 > 部署

AZURE_DEPLOYMENT_NAME=your_azure_deployment_name

# Azure OpenAI 资源名称 | [可选] | 格式：Azure OpenAI 资源名称

# 示例：your-openai-resource

# 获取方式：Azure 门户 > Azure OpenAI 服务

AZURE_RESOURCE_NAME=your_azure_resource_name

# Azure OpenAI API 密钥 | [可选] | 格式：Azure 密钥

# 示例：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6

# 获取方式：Azure 门户 > Azure OpenAI > 密钥和终结点

AZURE_API_KEY=your_azure_api_key

# Azure OpenAI 基本 URL | [可选] | 格式：https://resource_name.openai.azure.com

# 示例：https://your-resource.openai.azure.com

# 获取方式：Azure 门户 > Azure OpenAI > 密钥和终结点

AZURE_BASE_URL=your_azure_base_url

# =============================================================================

# 5. 第三方服务集成

# =============================================================================

# 支付、电子邮件、代码执行、分析和其它第三方服务的配置。

#

# 必需配置：根据业务需求选择

# 可选配置：所有配置均为可选，根据功能需求启用

# 相关文档：每个服务提供商的集成文档

# =============================================================================

# Stripe 支付 Webhook 密钥 | [必需] | 格式：whsec_...

# 示例：whsec_1234567890abcdef1234567890abcdef1234567890

# 获取方式：Stripe 仪表盘 > 工作台 > Webhooks > 签名密钥

STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Stripe 支付密钥 | [可选] | 格式：sk_test_... 或 sk_live_...

# 示例：sk_test_1234567890abcdef1234567890abcdef1234567890

# 获取方式：Stripe 仪表盘 > API 密钥

STRIPE_SECRET_KEY=your_stripe_secret_key

# Resend 电子邮件服务 API 密钥 | [可选] | 格式：re_...

# 示例：re_1234567890abcdef1234567890abcdef1234567890

# 获取方式：https://resend.com/api-keys

RESEND_API_KEY=your_resend_api_key

# 电子邮件发件人地址 | [可选] | 格式：<EMAIL>

# 示例：<EMAIL> 或 <EMAIL>

# 获取方式：配置您的域名电子邮件

RESEND_FROM=<EMAIL>

# E2B 代码执行环境 API 密钥 | [可选] | 格式：e2b_...

# 示例：e2b_1234567890abcdef1234567890abcdef1234567890

# 获取方式：https://e2b.dev/dashboard

E2B_API_KEY=your_e2b_api_key

# Daytona 代码执行环境 API 密钥 | [可选] | 格式：dtn_...
# 示例：dtn_1234567890abcdef1234567890abcdef1234567890
# 获取方式：https://app.daytona.io/dashboard/settings

DAYTONA_API_KEY=your_daytona_api_key

# 沙箱默认提供商 | [必选] | 格式：e2b 或 daytona
# 示例：e2b（使用E2B作为默认沙箱）或 daytona（使用Daytona作为默认沙箱）
# 获取方式：根据您的沙箱提供商选择

NEXT_PUBLIC_SANDBOX_DEFAULT_PROVIDER=e2b

# 沙箱部署构建默认提供商 | [必选] | 格式：daytona 或 e2b
# 示例：daytona（使用Daytona作为默认沙箱） 或 e2b（使用E2B作为默认沙箱）
# 获取方式：根据您的沙箱提供商选择

NEXT_PUBLIC_SANDBOX_BUILDER_DEFAULT_PROVIDER=daytona


# PostHog 分析项目 API 密钥 | [可选] | 格式：phc_...

# 示例：phc_1234567890abcdef1234567890abcdef1234567890

# 获取方式：PostHog 项目设置 > API 密钥

NEXT_PUBLIC_POSTHOG_KEY=your_posthog_project_api_key

# PostHog 分析服务主机 | [可选] | 格式：https://region.i.posthog.com

# 示例：https://us.i.posthog.com 或 https://eu.i.posthog.com

# 获取方式：PostHog 项目设置 > 项目 API 密钥

NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# =============================================================================

# 6. Cloudflare 服务配置

# =============================================================================

# 与 Cloudflare 相关服务的配置，包括 AI Gateway、区域管理等。

#

# 必需配置：CLOUDFLARE_ACCOUNT_ID, CLOUDFLARE_API_TOKEN（如果使用 Cloudflare 服务）

# 可选配置：CLOUDFLARE_AIGATEWAY_NAME, CLOUDFLARE_ZONE_ID

# 相关文档：https://developers.cloudflare.com/

# =============================================================================

# Cloudflare 账户 ID | [必需] | 格式：32 位十六进制字符串

# 示例：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6

# 获取方式：Cloudflare 仪表盘 > 右侧边栏账户 ID

CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id

# Cloudflare API 令牌 | [必需] | 格式：Cloudflare API 令牌

# 示例：****************************************

# 获取方式：Cloudflare 仪表盘 > 我的个人资料 > API 令牌 > 创建令牌

# Libra API 令牌摘要

# 此 API 令牌将影响以下帐户和区域，以及它们各自的权限

# Libra - Browser Rendering:Edit, D1:Edit, Workers R2 Storage:Edit, Workers KV Storage:Edit, Workers Scripts:Edit
#All zones - Workers Routes:Edit, SSL and Certificates:Edit, Cache Purge:Purge

CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# Cloudflare AI Gateway 名称 | [可选] | 格式：网关名称

# 示例：libra-ai-gateway 或 production-gateway

# 获取方式：Cloudflare 仪表盘 > AI > AI Gateway

CLOUDFLARE_AIGATEWAY_NAME=your_cloudflare_aigateway_name

# Cloudflare 区域 ID | [可选] | 格式：32 位十六进制字符串

# 主域名：libra.dev

# 示例：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6

# 获取方式：Cloudflare 仪表盘 > 域名 > 右侧边栏区域 ID

CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id

# =============================================================================

# 7. GitHub 集成配置

# =============================================================================

# 与 GitHub 仓库访问和项目管理相关的配置。

#

# 必需配置：如果使用 GitHub 集成功能

# 可选配置：所有配置均为可选

# 相关文档：https://docs.github.com/en/rest

# =============================================================================

# GitHub 访问令牌 | [可选] | 格式：ghp_... 或 github_pat_...

# 示例：ghp_1234567890abcdef1234567890abcdef1234567890

# 获取方式：GitHub 设置 > 开发者设置 > 个人访问令牌

LIBRA_GITHUB_TOKEN=your_github_token

# GitHub 组织或用户名 | [可选] | 格式：GitHub 用户名或组织名称

# 示例：your-username 或 your-organization

# 获取方式：GitHub 仓库 URL 中的所有者名称

LIBRA_GITHUB_OWNER=your_github_owner

# GitHub 仓库名称 | [可选] | 格式：仓库名称

# 示例：libra 或 your-project-name

# 获取方式：GitHub 仓库 URL 中的仓库名称

LIBRA_GITHUB_REPO=your_github_repo

# =============================================================================

# 8. 开发和调试配置

# =============================================================================

# 开发环境、调试和实验功能的配置选项。

#

# 必需配置：无

# 可选配置：所有配置均为可选，用于开发和调试

# 相关文档：项目内部文档

# =============================================================================

# 日志级别 | [可选] | 格式：DEBUG, INFO, WARN, ERROR

# 示例：ERROR（生产环境）或 DEBUG（开发环境）

# 获取方式：根据环境和调试需求设置

LOG_LEVEL=ERROR

# 增强提示模式 | [可选] | 格式：TRUE 或 FALSE

# 示例：TRUE（启用增强提示）或 FALSE（禁用）

# 获取方式：根据功能需求设置

ENHANCED_PROMPT=FALSE

# AI 推理功能开关 | [可选] | 格式：TRUE 或 FALSE

# 示例：TRUE（启用推理）或 FALSE（禁用）

# 获取方式：根据 AI 功能需求设置

REASONING_ENABLED=FALSE

# Payload CMS 密钥 | [可选，已禁用] | 格式：Payload 密钥

# 示例：payload-secret-key-1234567890abcdef

# 获取方式：Payload CMS 配置（当前未使用）

# PAYLOAD_SECRET=your_payload_secret

# =============================================================================

# 配置完成提醒

# =============================================================================

# 🎉 配置完成后，请将此文件重命名为 .env 并重启应用程序。

# ⚠️ 确保 .env 文件已添加到 .gitignore 中，以避免敏感信息泄露。

# 📖 更多配置说明，请参考项目文档和各服务提供商的官方文档。

# =============================================================================

# =============================================================================

# 9. GitHub 应用程序集成配置

# =============================================================================

# GitHub 应用程序集成的配置，使其能够进行深入的仓库访问和 Webhooks。

#

# 必需配置：如果使用 GitHub 应用程序功能进行仓库管理和自动化

# 可选配置：除非需要 GitHub 应用程序功能，否则所有配置均为可选

# 相关文档：https://docs.github.com/en/developers/apps

# =============================================================================

# GitHub 应用程序_slug名称 | [可选] | 格式：应用程序名称

# 示例：nextify-limited 或 your-app-name

# 获取方式：GitHub 开发者设置 > GitHub 应用程序 > 应用程序_slug

GITHUB_APP_SLUG=nextify-limited

# GitHub 应用程序 ID | [可选] | 格式：数字应用程序 ID

# 示例：1234567

# 获取方式：GitHub 开发者设置 > GitHub 应用程序 > 应用程序 ID

GITHUB_APP_ID=your_github_app_id

# GitHub 应用程序私钥 | [可选] | 格式：带有换行符的 RSA 私钥

# 示例： "-----BEGIN RSA PRIVATE KEY-----

...

-----END RSA PRIVATE KEY-----"

# 获取方式：GitHub 开发者设置 > GitHub 应用程序 > 生成私钥

GITHUB_APP_PRIVATE_KEY=your_github_app_private_key

# GitHub 应用程序 OAuth 客户端 ID | [可选] | 格式：OAuth 客户端标识符

# 示例：12345678901123

# 获取方式：GitHub 开发者设置 > GitHub 应用程序 > 客户端 ID

GITHUB_APP_CLIENT_ID=your_github_app_client_id

# GitHub 应用程序 OAuth 客户端密钥 | [可选] | 格式：OAuth 客户端密钥

# 示例：abcdef1234567890abcdef1234567890

# 获取方式：GitHub 开发者设置 > GitHub 应用程序 > 生成客户端密钥

GITHUB_APP_CLIENT_SECRET=your_github_app_client_secret

# GitHub 应用程序公共 URL | [可选] | 格式：https://github.com/apps/app-name

# 示例：https://github.com/apps/nextify-limited

# 获取方式：GitHub 开发者设置 > GitHub 应用程序 > 公共链接

NEXT_PUBLIC_GITHUB_APP_URL=https://github.com/apps/your_github_app_name

# GitHub Webhook 密钥 | [可选] | 格式：Webhook 密钥字符串

# 示例：abcdef1234567890abcdef1234567890

# 获取方式：GitHub 开发者设置 > GitHub 应用程序 > Webhook 密钥

GITHUB_WEBHOOK_SECRET=your_github_webhook_secret

# =============================================================================

# 10. DNS 和域名配置

# =============================================================================

# 域名验证和 DNS 管理的配置，主要用于 Cloudflare SaaS。

#

# 必需配置：如果使用自定义域名或 Cloudflare SaaS 功能

# 可选配置：除非需要域名功能，否则所有配置均为可选

# 相关文档：https://developers.cloudflare.com/cloudflare-for-platforms/cloudflare-for-saas/

# 获取方式: Cloudflare Dashboard > SaaS zone configuration

# Cloudflare DCV 验证 ID | [必需] | 格式：32 位十六进制字符串
# 示例：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# 获取方式：Cloudflare 仪表盘 > SSL/TLS > 自定义主机名 > DCV 验证 ID
# 用途：用于域名控制验证，确保自定义域名的 SSL 证书颁发
NEXT_PUBLIC_CLOUDFLARE_DCV_VERIFICATION_ID=your_dcv_verification_id

# Cloudflare SaaS 区域 ID | [必需] | 格式：32 位十六进制字符串
# 示例：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# 获取方式：Cloudflare 仪表盘 > Cloudflare for SaaS > 区域配置
# 用途：管理 SaaS 客户的自定义域名和 SSL 证书
CLOUDFLARE_SAAS_ZONE_ID=your_cloudflare_saas_zone_id

# =============================================================================
# 11. Cloudflare 数据库和存储服务 ID
# =============================================================================
# Cloudflare 特定服务的资源标识符，用于数据库连接和键值存储。

# Cloudflare Hyperdrive 数据库 ID | [可选] | 格式：32 位十六进制字符串
# 示例：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# 获取方式：Cloudflare 仪表盘 > Hyperdrive > 数据库配置
# 用途：加速数据库连接，提供全球分布式数据库访问
HYPERDRIVE_ID=

# Cloudflare KV 命名空间 ID | [可选] | 格式：32 位十六进制字符串
# 示例：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
# 获取方式：Cloudflare 仪表盘 > Workers & Pages > KV > 命名空间
# 用途：全球分布式键值存储，用于缓存和会话管理
KV_NAMESPACE_ID=

# =============================================================================
# 12. 网络路由配置
# =============================================================================
# 客户端路由和网络访问的 IP 地址配置。

# 客户端 APEX 路由 IP 地址 | [可选] | 格式：IPv4 地址
# 示例：************* 或 ***********
# 获取方式：网络管理员提供或服务器配置
# 用途：指定客户端访问的主要路由 IP 地址，用于解决 cname flattening 问题
NEXT_PUBLIC_CUSTOMERS_IP_ADDRESS=
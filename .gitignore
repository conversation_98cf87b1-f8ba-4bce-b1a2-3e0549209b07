**/node_modules
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# expo
**/.expo/*
**/expo-env.d.ts

# next.js
**/.next/*
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.bun-debug.log*

# local env files
.env
**.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.prod
.env.staging

# vercel
.vercel

# typescript
*.tsbuildinfo

# os
.DS_Store
THUMBS_DB
thumbs.db

# ui

dist/
.DS_Store
THUMBS_DB
node_modules/

# Cloudflare D1
**/.wrangler/*
**/.dev.vars
.turbo

.idea/
